# MultiCharts Trading Strategies

本仓库包含基于MultiCharts平台开发的量化交易策略。

## 📊 策略列表

### 🎯 反包接力策略
- **策略名称**: 虚晃一枪，反包接力
- **策略类型**: 短线交易策略  
- **适用市场**: A股市场
- **文件位置**: `反包接力/`
- **核心逻辑**: 识别假阴线形态，捕捉次日反包机会

## 🚀 快速开始

### 环境要求
- MultiCharts 平台
- A股实时数据源
- 基础的技术分析知识

### 使用步骤
1. 📥 下载对应的策略文件
2. 📊 在MultiCharts中导入策略
3. ⚙️ 根据使用指南配置参数
4. 🔍 进行充分的回测验证
5. 💰 小资金试运行后正式使用

## 📁 项目结构

```
mc/
├── README.md                    # 项目说明
├── 反包接力/                   # 反包接力策略
│   ├── FakeRedCandle_Simple.txt           # 简化版策略代码
│   ├── FakeRedCandle_Reversal_Strategy.txt # 完整版策略代码
│   ├── Strategy_Usage_Guide.md            # 使用指南
│   └── Backtest_Optimization_Guide.md     # 回测优化指南
```

## 🔧 策略特点

### 反包接力策略
- ✅ **入场精准**: 基于假阴线形态识别
- ✅ **风险可控**: 严格的止损止盈机制
- ✅ **适应性强**: 可根据市场环境调整参数
- ✅ **易于使用**: 提供简化版和完整版两种选择

## 📈 策略表现

> **注意**: 历史表现不代表未来收益，请务必进行充分的回测和风险评估。

### 关键指标
- 🎯 **目标胜率**: > 50%
- 📊 **目标盈亏比**: > 1.2
- 📉 **最大回撤控制**: < 15%
- ⏱️ **平均持仓时间**: 2-4天

## 🛠️ 安装使用

### 1. 克隆仓库
```bash
git clone https://gitee.com/your-username/mc.git
cd mc
```

### 2. 导入策略
1. 打开MultiCharts PowerLanguage Editor
2. 创建新的策略文件
3. 复制对应策略代码
4. 编译并应用到图表

### 3. 参数配置
参考各策略目录下的使用指南进行参数设置。

## 📚 文档说明

- **Strategy_Usage_Guide.md**: 详细的策略使用说明
- **Backtest_Optimization_Guide.md**: 回测和参数优化指南
- **FakeRedCandle_Simple.txt**: 简化版策略，适合初学者
- **FakeRedCandle_Reversal_Strategy.txt**: 完整版策略，功能更丰富

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进策略！

### 贡献方式
1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## ⚠️ 风险提示

- 📊 **策略基于历史数据**: 过往表现不代表未来收益
- 💰 **资金管理重要**: 请合理控制仓位和风险
- 🔍 **充分测试**: 实盘前务必进行充分的模拟交易
- 📈 **市场有风险**: 投资需谨慎，请根据自身情况使用

## 📞 联系方式

- 📧 **Issues**: 通过GitHub/Gitee Issues提交问题
- 💬 **讨论**: 欢迎在Issues中讨论策略优化建议

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**免责声明**: 本仓库内容仅供学习研究使用，不构成投资建议。使用者应当具备相应的金融知识和风险承受能力，并对自己的投资决策负责。
