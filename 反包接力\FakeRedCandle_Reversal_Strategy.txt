{
"虚晃一枪，反包接力" MultiCharts交易策略
===========================================

策略名称: FakeRedCandle_Reversal_Strategy
策略类型: 日内/短线交易策略
适用市场: A股市场
时间周期: 日线图

策略逻辑:
1. 识别假阴线形态（高开低走收阴，但成交量放大）
2. 验证主力承接信号（分时图多次承接）
3. 次日反包涨停确认入场

核心条件:
- 假阴线当日成交额放大至前日2倍以上
- 换手率适中（5-15%）
- 收盘价不跌破前日涨停价2%以上
- 次日竞价高开且快速封板

风险控制:
- 止损: 跌破假阴线最低价
- 止盈: 涨停板或达到预设收益目标
- 仓位管理: 单笔交易不超过总资金10%
}

inputs:
    // 基础参数设置
    VolumeRatioThreshold(2.0),      // 成交量放大倍数阈值
    TurnoverRateMin(5.0),           // 最小换手率
    TurnoverRateMax(15.0),          // 最大换手率
    PriceDropLimit(2.0),            // 收盘价跌幅限制(%)
    OpenGapMin(1.0),                // 次日最小高开幅度(%)
    StopLossPercent(5.0),           // 止损百分比
    TakeProfitPercent(8.0),         // 止盈百分比
    MaxPositionPercent(10.0),       // 最大仓位百分比
    
    // 技术指标参数
    MA_Period(20),                  // 均线周期
    RSI_Period(14),                 // RSI周期
    RSI_Oversold(30),              // RSI超卖线
    RSI_Overbought(70);            // RSI超买线

variables:
    // 价格变量
    PrevClose(0),                   // 前日收盘价
    PrevHigh(0),                    // 前日最高价
    PrevLow(0),                     // 前日最低价
    PrevVolume(0),                  // 前日成交量
    CurrentVolume(0),               // 当日成交量
    
    // 计算变量
    VolumeRatio(0),                 // 成交量比率
    TurnoverRate(0),                // 换手率（需要外部数据）
    PriceDropPercent(0),            // 价格跌幅百分比
    OpenGapPercent(0),              // 开盘缺口百分比
    
    // 技术指标
    MA_Value(0),                    // 移动平均线
    RSI_Value(0),                   // RSI指标
    
    // 交易状态
    FakeRedCandleDetected(false),   // 假阴线检测标志
    EntryPrice(0),                  // 入场价格
    StopLossPrice(0),               // 止损价格
    TakeProfitPrice(0),             // 止盈价格
    
    // 辅助变量
    SharesFloat(0),                 // 流通股本（需要外部数据）
    MarketCap(0);                   // 市值

// 初始化
once begin
    // 设置交易参数
    SetStopContract;
    SetExitOnClose;
end;

// 主策略逻辑
if Date > Date[1] then begin
    
    // 获取前一日数据
    PrevClose = Close[1];
    PrevHigh = High[1];
    PrevLow = Low[1];
    PrevVolume = Volume[1];
    CurrentVolume = Volume;
    
    // 计算技术指标
    MA_Value = Average(Close, MA_Period);
    RSI_Value = RSI(Close, RSI_Period);
    
    // 计算成交量比率
    if PrevVolume > 0 then
        VolumeRatio = CurrentVolume / PrevVolume
    else
        VolumeRatio = 0;
    
    // 计算价格跌幅百分比
    if PrevClose > 0 then
        PriceDropPercent = ((PrevClose - Close) / PrevClose) * 100
    else
        PriceDropPercent = 0;
    
    // 计算开盘缺口百分比
    if PrevClose > 0 then
        OpenGapPercent = ((Open - PrevClose) / PrevClose) * 100
    else
        OpenGapPercent = 0;
    
    // 假阴线检测逻辑
    FakeRedCandleDetected = false;
    
    // 条件1: 当日收阴线（收盘价低于开盘价）
    if Close < Open then begin
        
        // 条件2: 成交量放大至前日2倍以上
        if VolumeRatio >= VolumeRatioThreshold then begin
            
            // 条件3: 换手率在合理范围内（这里用成交量/流通股本估算）
            // 注意：实际使用时需要接入真实的换手率数据
            TurnoverRate = (CurrentVolume * Close) / (SharesFloat * 10000) * 100;
            
            if TurnoverRate >= TurnoverRateMin and TurnoverRate <= TurnoverRateMax then begin
                
                // 条件4: 收盘价跌幅不超过限制
                if PriceDropPercent <= PriceDropLimit then begin
                    
                    // 条件5: 股价在均线上方（趋势向上）
                    if Close > MA_Value then begin
                        
                        // 条件6: RSI不在超卖区域（避免弱势股）
                        if RSI_Value > RSI_Oversold then begin
                            
                            FakeRedCandleDetected = true;
                            
                            // 在图表上标记假阴线
                            Plot1(Low - (High - Low) * 0.1, "FakeRed");
                            
                        end;
                    end;
                end;
            end;
        end;
    end;
    
    // 入场逻辑：次日反包
    if FakeRedCandleDetected[1] = true and MarketPosition = 0 then begin
        
        // 条件1: 次日高开
        if OpenGapPercent >= OpenGapMin then begin
            
            // 条件2: 快速上涨（这里简化为开盘后30分钟内涨幅超过3%）
            if (High - Open) / Open * 100 >= 3.0 then begin
                
                // 入场
                EntryPrice = High;
                Buy("FakeRed_Entry") next bar at EntryPrice stop;
                
                // 设置止损止盈
                StopLossPrice = Low[1] * (1 - StopLossPercent / 100);
                TakeProfitPrice = EntryPrice * (1 + TakeProfitPercent / 100);
                
                // 在图表上标记入场点
                Plot2(EntryPrice, "Entry");
                
            end;
        end;
    end;
    
end;

// 出场逻辑
if MarketPosition > 0 then begin
    
    // 止损
    if Low <= StopLossPrice then begin
        Sell("StopLoss") next bar at StopLossPrice stop;
        Plot3(StopLossPrice, "StopLoss");
    end;
    
    // 止盈
    if High >= TakeProfitPrice then begin
        Sell("TakeProfit") next bar at TakeProfitPrice limit;
        Plot4(TakeProfitPrice, "TakeProfit");
    end;
    
    // 时间止损（持仓超过3天）
    if BarsSinceEntry >= 3 then begin
        Sell("TimeStop") next bar at market;
    end;
    
    // 技术止损（跌破重要支撑）
    if Close < MA_Value then begin
        Sell("TechStop") next bar at market;
    end;
    
end;

// 风险管理
if MarketPosition = 0 then begin
    
    // 计算仓位大小（基于账户资金和风险百分比）
    // 这里需要根据实际账户资金进行调整
    
    // 避免在市场极端情况下交易
    if RSI_Value > RSI_Overbought or RSI_Value < RSI_Oversold then begin
        // 暂停交易
    end;
    
end;

// 绘制辅助线
Plot5(MA_Value, "MA");
Plot6(RSI_Value, "RSI");

// 输出调试信息
if GetAppInfo(aiStrategyAuto) = 1 then begin
    Print("Date: ", Date, " VolumeRatio: ", VolumeRatio:4:2, 
          " PriceDropPercent: ", PriceDropPercent:4:2,
          " FakeRedDetected: ", FakeRedCandleDetected);
end;
