// ========================================
// 虚晃一枪，反包接力 - 简化版策略
// Strategy Name: FakeRedCandle_Simple
// ========================================

inputs:
    VolRatio(2.0),          // 成交量放大倍数
    MaxDrop(2.0),           // 最大跌幅%
    MinGap(1.0),            // 最小高开%
    StopLoss(5.0),          // 止损%
    TakeProfit(8.0),        // 止盈%
    HoldDays(3);            // 最大持仓天数

variables:
    FakeRed(false),         // 假阴线标志
    EntryPrice(0),          // 入场价
    StopPrice(0),           // 止损价
    TargetPrice(0),         // 目标价
    VolRatioCalc(0),        // 成交量比率
    DropPercent(0),         // 跌幅百分比
    GapPercent(0);          // 缺口百分比

// 假阴线检测
FakeRed = false;

// 检测条件：收阴线 + 成交量放大 + 跌幅有限
if Close < Open then begin
    
    // 计算成交量比率
    if Volume[1] > 0 then
        VolRatioCalc = Volume / Volume[1]
    else
        VolRatioCalc = 0;
    
    // 计算跌幅百分比
    if Close[1] > 0 then
        DropPercent = (Close[1] - Close) / Close[1] * 100
    else
        DropPercent = 0;
    
    // 假阴线条件判断
    if VolRatioCalc >= VolRatio and 
       DropPercent <= MaxDrop and
       Close > Average(Close, 20) then begin
        
        FakeRed = true;
        Plot1(Low - (High-Low)*0.1, "FakeRed");
        
    end;
end;

// 入场逻辑：次日反包
if FakeRed[1] = true and MarketPosition = 0 then begin
    
    // 计算高开幅度
    if Close[1] > 0 then
        GapPercent = (Open - Close[1]) / Close[1] * 100
    else
        GapPercent = 0;
    
    // 高开且快速上涨
    if GapPercent >= MinGap and 
       (High - Open) / Open * 100 >= 2.0 then begin
        
        EntryPrice = High;
        Buy("Entry") next bar at EntryPrice stop;
        
        // 设置止损止盈
        StopPrice = Low[1];
        TargetPrice = EntryPrice * (1 + TakeProfit/100);
        
        Plot2(EntryPrice, "Entry");
        
    end;
end;

// 出场逻辑
if MarketPosition > 0 then begin
    
    // 止损
    if Low <= StopPrice then
        Sell("StopLoss") next bar at StopPrice stop;
    
    // 止盈
    if High >= TargetPrice then
        Sell("TakeProfit") next bar at TargetPrice limit;
    
    // 时间止损
    if BarsSinceEntry >= HoldDays then
        Sell("TimeStop") next bar at market;
        
end;

// 绘制辅助线
Plot3(Average(Close, 20), "MA20");
