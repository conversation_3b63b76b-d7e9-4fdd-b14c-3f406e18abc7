@echo off
echo ========================================
echo     发布项目到Gitee自动化脚本
echo ========================================
echo.

REM 检查是否在正确的目录
if not exist "README.md" (
    echo 错误：请在项目根目录运行此脚本！
    pause
    exit /b 1
)

echo 1. 检查Git状态...
git status
echo.

echo 2. 添加所有更改...
git add .
echo.

echo 3. 提交更改...
set /p commit_msg="请输入提交信息 (默认: Update project): "
if "%commit_msg%"=="" set commit_msg=Update project
git commit -m "%commit_msg%"
echo.

echo 4. 检查远程仓库配置...
git remote -v
echo.

REM 检查是否已配置远程仓库
git remote get-url origin >nul 2>&1
if errorlevel 1 (
    echo 警告：未检测到远程仓库配置！
    echo 请按照以下步骤配置：
    echo.
    echo 1. 在Gitee创建新仓库
    echo 2. 复制仓库地址
    echo 3. 运行命令：git remote add origin [仓库地址]
    echo.
    set /p repo_url="请输入Gitee仓库地址 (或按Enter跳过): "
    if not "%repo_url%"=="" (
        echo 添加远程仓库...
        git remote add origin %repo_url%
        echo 远程仓库已添加！
    ) else (
        echo 跳过远程仓库配置。
        pause
        exit /b 0
    )
)

echo 5. 推送到Gitee...
git push -u origin main
echo.

if errorlevel 0 (
    echo ========================================
    echo     🎉 项目已成功发布到Gitee！
    echo ========================================
    echo.
    echo 接下来您可以：
    echo 1. 访问Gitee仓库查看项目
    echo 2. 完善仓库描述和标签
    echo 3. 邀请其他开发者协作
    echo.
) else (
    echo ========================================
    echo     ❌ 发布过程中出现错误！
    echo ========================================
    echo.
    echo 可能的原因：
    echo 1. 网络连接问题
    echo 2. 认证失败（需要输入用户名密码）
    echo 3. 远程仓库地址错误
    echo 4. 分支冲突
    echo.
    echo 请检查错误信息并重试。
    echo.
)

pause
